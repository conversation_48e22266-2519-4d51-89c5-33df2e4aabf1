/**
 * Script Final para Evolution API - GA4 Python MCP
 * 
 * Este é o script definitivo para usar na Evolution API.
 * Ele usa o servidor Python MCP diretamente via HTTP.
 * 
 * @param {Object} input - Parâmetros de entrada
 * @param {string} input.property_id - ID da propriedade GA4 (padrão: "*********")
 * @param {string} input.action - Ação ("report", "realtime", "metadata")
 * @param {Array} input.metrics - Métricas GA4 (ex: ["activeUsers", "sessions"])
 * @param {Array} input.dimensions - Dimensões GA4 (ex: ["date", "country"])
 * @param {string} input.date_range - Período ("last7days", "last30days", "today", "yesterday")
 * @param {number} input.limit - Número máximo de registros (padrão: 10)
 * @param {string} input.server_url - URL do servidor (padrão: "http://localhost:8080" ou URL do túnel Cloudflare)
 * @param {Object} context - Contexto da Evolution (não usado)
 */
module.exports = async function(input, context) {
  const axios = require('axios');
  
  try {
    console.log('🚀 GA4 Python MCP - Evolution API');
    
    // Configurações
    const config = {
      property_id: input.property_id || "*********",
      action: input.action || "report",
      metrics: input.metrics || ["activeUsers"],
      dimensions: input.dimensions || null,
      date_range: input.date_range || "last30days",
      limit: input.limit || 10,
      metadata_type: input.metadata_type || "all",
      server_url: input.server_url || "http://localhost:8080"
    };
    
    console.log(`📊 Configuração: ${JSON.stringify(config, null, 2)}`);
    
    // Faz requisição para o servidor Python
    const response = await axios.post(
      `${config.server_url}/ga4-evolution`,
      config,
      {
        timeout: 30000,
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );
    
    if (response.data.success) {
      console.log('✅ Dados obtidos com sucesso');
      
      return {
        success: true,
        data: response.data.data,
        source: "ga4-python-mcp",
        timestamp: new Date().toISOString(),
        config: config
      };
    } else {
      throw new Error(response.data.error || 'Erro desconhecido do servidor');
    }
    
  } catch (error) {
    console.error('❌ Erro na integração:', error.message);
    
    // Fallback: tenta usar integração direta se o servidor não estiver disponível
    if (error.code === 'ECONNREFUSED' || error.code === 'ENOTFOUND') {
      console.log('⚠️ Servidor não disponível, tentando integração direta...');
      
      try {
        return await fallbackDirectIntegration(input, context);
      } catch (fallbackError) {
        return {
          success: false,
          error: `Servidor indisponível e fallback falhou: ${fallbackError.message}`,
          source: "ga4-python-mcp",
          timestamp: new Date().toISOString()
        };
      }
    }
    
    return {
      success: false,
      error: error.message,
      source: "ga4-python-mcp",
      timestamp: new Date().toISOString()
    };
  }
};

/**
 * Integração direta como fallback (requer credenciais nos secrets)
 */
async function fallbackDirectIntegration(input, context) {
  const { GoogleAuth } = require('google-auth-library');
  const axios = require('axios');
  
  console.log('🔄 Executando integração direta...');
  
  // Verifica se as credenciais estão disponíveis
  if (!context.secrets || !context.secrets.GA4_CREDENTIALS) {
    throw new Error('Credenciais GA4 não encontradas nos secrets da Evolution');
  }
  
  const config = {
    property_id: input.property_id || "*********",
    metrics: input.metrics || ["activeUsers"],
    dimensions: input.dimensions || null,
    date_range: input.date_range || "last30days",
    limit: input.limit || 10
  };
  
  try {
    // Carrega credenciais
    const serviceAccount = JSON.parse(context.secrets.GA4_CREDENTIALS);
    
    const auth = new GoogleAuth({
      credentials: serviceAccount,
      scopes: ['https://www.googleapis.com/auth/analytics.readonly']
    });
    
    const client = await auth.getClient();
    const accessToken = await client.getAccessToken();
    
    console.log('✅ Token gerado via fallback');
    
    // Prepara requisição GA4
    const requestBody = {
      dimensions: config.dimensions ? config.dimensions.map(d => ({ name: d })) : [],
      metrics: config.metrics.map(m => ({ name: m })),
      dateRanges: [{ 
        startDate: getDateFromRange(config.date_range).start,
        endDate: getDateFromRange(config.date_range).end
      }],
      limit: config.limit
    };
    
    // Faz requisição para GA4
    const response = await axios.post(
      `https://analyticsdata.googleapis.com/v1beta/properties/${config.property_id}:runReport`,
      requestBody,
      {
        headers: {
          'Authorization': `Bearer ${accessToken.token}`,
          'Content-Type': 'application/json'
        },
        timeout: 30000
      }
    );
    
    console.log('✅ Dados obtidos via fallback');
    
    // Formata resposta simples
    const rows = response.data.rows || [];
    const formattedData = rows.map(row => {
      const result = {};
      
      // Adiciona dimensões
      if (config.dimensions) {
        config.dimensions.forEach((dim, index) => {
          result[dim] = row.dimensionValues[index]?.value || '';
        });
      }
      
      // Adiciona métricas
      config.metrics.forEach((metric, index) => {
        result[metric] = row.metricValues[index]?.value || '0';
      });
      
      return result;
    });
    
    return {
      success: true,
      data: formatSimpleTable(formattedData, config.dimensions, config.metrics),
      source: "ga4-direct-fallback",
      timestamp: new Date().toISOString(),
      config: config
    };
    
  } catch (error) {
    console.error('❌ Erro no fallback:', error.message);
    throw error;
  }
}

/**
 * Converte range de data em datas específicas
 */
function getDateFromRange(range) {
  const today = new Date();
  const formatDate = (date) => date.toISOString().split('T')[0];
  
  switch (range) {
    case 'today':
      return { start: formatDate(today), end: formatDate(today) };
    case 'yesterday':
      const yesterday = new Date(today);
      yesterday.setDate(yesterday.getDate() - 1);
      return { start: formatDate(yesterday), end: formatDate(yesterday) };
    case 'last7days':
      const week = new Date(today);
      week.setDate(week.getDate() - 7);
      return { start: formatDate(week), end: formatDate(today) };
    case 'last30days':
    default:
      const month = new Date(today);
      month.setDate(month.getDate() - 30);
      return { start: formatDate(month), end: formatDate(today) };
  }
}

/**
 * Formata dados em tabela simples
 */
function formatSimpleTable(data, dimensions, metrics) {
  if (!data || data.length === 0) {
    return "📊 Nenhum dado encontrado.";
  }
  
  const headers = [...(dimensions || []), ...metrics];
  let result = "📊 **Relatório GA4**\n\n";
  
  // Cabeçalho
  result += "| " + headers.join(" | ") + " |\n";
  result += "| " + headers.map(() => "---").join(" | ") + " |\n";
  
  // Dados
  data.slice(0, 10).forEach(row => {
    const values = headers.map(header => row[header] || '');
    result += "| " + values.join(" | ") + " |\n";
  });
  
  if (data.length > 10) {
    result += `\n... e mais ${data.length - 10} registros`;
  }
  
  return result;
}

// Exemplo de uso para teste
if (require.main === module) {
  const testInput = {
    property_id: "*********",
    action: "report",
    metrics: ["activeUsers", "sessions"],
    dimensions: ["date"],
    date_range: "last7days",
    limit: 5
  };
  
  module.exports(testInput, {}).then(result => {
    console.log('Resultado do teste:', JSON.stringify(result, null, 2));
  }).catch(console.error);
}
