@echo off
echo 🚀 GA4 Python MCP - Cloudflare Tunnel
echo =====================================

cd /d "%~dp0"

echo 📋 Verificando ambiente...
if not exist "venv\Scripts\python.exe" (
    echo ❌ Ambiente virtual não encontrado
    echo 💡 Execute primeiro: python install.py
    pause
    exit /b 1
)

if not exist "credenciais-ga4.json" (
    echo ❌ Arquivo credenciais-ga4.json não encontrado
    echo 💡 Copie o arquivo de credenciais para este diretório
    pause
    exit /b 1
)

echo ✅ Ambiente OK

echo.
echo 🌐 Iniciando túnel Cloudflare (modo persistente)...
echo ⏳ Aguarde alguns segundos...
echo ⚠️ Se der erro de DNS, é normal - aguarde alguns minutos
echo.

venv\Scripts\python.exe start_tunnel_persistent.py --port 8080

echo.
echo 🛑 Túnel encerrado
pause
