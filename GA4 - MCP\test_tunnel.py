"""
Script para testar se o túnel Cloudflare está funcionando.
"""
import json
import requests
import time
import sys
from pathlib import Path


def test_tunnel_from_file():
    """Testa o túnel usando informações do arquivo tunnel_info.json."""
    tunnel_file = Path("tunnel_info.json")
    
    if not tunnel_file.exists():
        print("❌ Arquivo tunnel_info.json não encontrado")
        print("💡 Execute primeiro: python cloudflare_tunnel.py")
        return False
    
    try:
        with open(tunnel_file, 'r') as f:
            tunnel_info = json.load(f)
        
        tunnel_url = tunnel_info.get('tunnel_url')
        if not tunnel_url:
            print("❌ URL do túnel não encontrada no arquivo")
            return False
        
        print(f"🌐 Testando túnel: {tunnel_url}")
        return test_tunnel_url(tunnel_url)
        
    except Exception as e:
        print(f"❌ Erro ao ler arquivo: {e}")
        return False


def test_tunnel_url(url, max_retries=3):
    """Testa uma URL específica do túnel."""
    print(f"🧪 Testando URL: {url}")
    
    for attempt in range(max_retries):
        try:
            print(f"   Tentativa {attempt + 1}/{max_retries}...")
            
            # Teste 1: Health check
            response = requests.get(
                f"{url}/health",
                timeout=10,
                headers={'User-Agent': 'GA4-MCP-Manual-Test'}
            )
            
            if response.status_code == 200:
                print("✅ Health check OK")
                try:
                    data = response.json()
                    print(f"   Resposta: {data}")
                except:
                    print(f"   Resposta: {response.text[:100]}")
                
                # Teste 2: Endpoint GA4
                print("🧪 Testando endpoint GA4...")
                ga4_response = requests.post(
                    f"{url}/ga4-evolution",
                    json={
                        "property_id": "330715799",
                        "action": "metadata",
                        "metadata_type": "metrics"
                    },
                    timeout=15,
                    headers={'Content-Type': 'application/json'}
                )
                
                if ga4_response.status_code == 200:
                    print("✅ Endpoint GA4 funcionando")
                    try:
                        ga4_data = ga4_response.json()
                        if ga4_data.get('success'):
                            print("✅ Dados GA4 obtidos com sucesso")
                        else:
                            print(f"⚠️ Erro nos dados GA4: {ga4_data.get('error')}")
                    except:
                        print("⚠️ Resposta GA4 não é JSON válido")
                else:
                    print(f"⚠️ Endpoint GA4 retornou status {ga4_response.status_code}")
                
                return True
            else:
                print(f"⚠️ Health check retornou status {response.status_code}")
                
        except requests.exceptions.ConnectionError as e:
            if "NameResolutionError" in str(e) or "getaddrinfo failed" in str(e):
                print(f"⚠️ DNS ainda não propagado (tentativa {attempt + 1})")
            else:
                print(f"⚠️ Erro de conexão: {e}")
        except requests.exceptions.Timeout:
            print(f"⚠️ Timeout na tentativa {attempt + 1}")
        except Exception as e:
            print(f"⚠️ Erro: {e}")
        
        if attempt < max_retries - 1:
            print("   Aguardando 5 segundos...")
            time.sleep(5)
    
    print("❌ Todas as tentativas falharam")
    return False


def test_local_server():
    """Testa se o servidor local está funcionando."""
    print("🏠 Testando servidor local...")
    
    try:
        response = requests.get("http://localhost:8080/health", timeout=5)
        if response.status_code == 200:
            print("✅ Servidor local funcionando")
            return True
        else:
            print(f"⚠️ Servidor local retornou status {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Servidor local não está rodando: {e}")
        return False


def main():
    """Função principal."""
    print("🧪 Teste Manual do Túnel Cloudflare")
    print("=" * 50)
    
    # Teste 1: Servidor local
    local_ok = test_local_server()
    
    print("\n" + "=" * 50)
    
    # Teste 2: Túnel
    if len(sys.argv) > 1:
        # URL fornecida como argumento
        tunnel_url = sys.argv[1]
        tunnel_ok = test_tunnel_url(tunnel_url)
    else:
        # Lê do arquivo
        tunnel_ok = test_tunnel_from_file()
    
    print("\n" + "=" * 50)
    print("📋 Resumo dos Testes:")
    print(f"   Servidor Local: {'✅ OK' if local_ok else '❌ FALHOU'}")
    print(f"   Túnel Cloudflare: {'✅ OK' if tunnel_ok else '❌ FALHOU'}")
    
    if tunnel_ok:
        print("\n🎉 Túnel funcionando! Pode usar na Evolution API.")
        
        # Mostra exemplo de uso
        tunnel_file = Path("tunnel_info.json")
        if tunnel_file.exists():
            try:
                with open(tunnel_file, 'r') as f:
                    tunnel_info = json.load(f)
                
                print("\n📋 Informações para Evolution API:")
                print(f"   URL: {tunnel_info.get('tunnel_url')}")
                print(f"   Endpoint: {tunnel_info.get('ga4_endpoint')}")
                
                print("\n💻 Exemplo de uso:")
                print("   const input = {")
                print(f'     server_url: "{tunnel_info.get("tunnel_url")}",')
                print('     property_id: "330715799",')
                print('     action: "report",')
                print('     metrics: ["activeUsers"]')
                print("   };")
                
            except:
                pass
    else:
        print("\n⚠️ Túnel com problemas. Possíveis soluções:")
        print("   1. Aguarde alguns minutos (DNS pode demorar)")
        print("   2. Reinicie o túnel: python cloudflare_tunnel.py")
        print("   3. Use servidor local: http://localhost:8080")
        print("   4. Verifique firewall/antivírus")
    
    return tunnel_ok


if __name__ == "__main__":
    success = main()
    
    if not success:
        print("\n💡 Para mais ajuda, consulte: GUIA_CLOUDFLARE_TUNNEL.md")
    
    input("\nPressione Enter para sair...")
    sys.exit(0 if success else 1)
