"""
Script de teste para validar a integração completa do GA4 Python MCP.
"""
import asyncio
import json
import requests
import time
from evolution_integration import main_evolution_function


def test_http_server():
    """Testa o servidor HTTP."""
    print("🧪 Testando servidor HTTP...")
    
    try:
        # Teste de health check
        response = requests.get("http://localhost:8080/health", timeout=10)
        if response.status_code == 200:
            print("✅ Health check OK")
            print(f"   Response: {response.json()}")
        else:
            print(f"❌ Health check falhou: {response.status_code}")
            return False
        
        # Teste de dados GA4
        data = {
            "property_id": "330715799",
            "metrics": ["activeUsers", "sessions"],
            "dimensions": ["date"],
            "date_range": "last7days",
            "limit": 5
        }
        
        response = requests.post(
            "http://localhost:8080/ga4-data",
            json=data,
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get("success"):
                print("✅ Dados GA4 obtidos via HTTP")
                print(f"   Registros: {result['data']['data']['row_count']}")
            else:
                print(f"❌ Erro nos dados GA4: {result.get('error')}")
                return False
        else:
            print(f"❌ Requisição HTTP falhou: {response.status_code}")
            return False
        
        # Teste do endpoint Evolution
        evolution_data = {
            "property_id": "330715799",
            "action": "report",
            "metrics": ["activeUsers"],
            "dimensions": ["date"],
            "date_range": "last7days",
            "limit": 3
        }
        
        response = requests.post(
            "http://localhost:8080/ga4-evolution",
            json=evolution_data,
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get("success"):
                print("✅ Endpoint Evolution funcionando")
                print("   Resultado:")
                print(result["data"][:200] + "..." if len(result["data"]) > 200 else result["data"])
            else:
                print(f"❌ Erro no endpoint Evolution: {result.get('error')}")
                return False
        else:
            print(f"❌ Endpoint Evolution falhou: {response.status_code}")
            return False
        
        return True
        
    except requests.exceptions.ConnectionError:
        print("❌ Não foi possível conectar ao servidor HTTP")
        print("💡 Certifique-se de que o servidor está rodando: python http_server.py")
        return False
    except Exception as e:
        print(f"❌ Erro no teste HTTP: {e}")
        return False


async def test_direct_integration():
    """Testa a integração direta Python."""
    print("\n🧪 Testando integração direta Python...")
    
    try:
        # Teste de relatório padrão
        params = {
            "property_id": "330715799",
            "action": "report",
            "metrics": ["activeUsers", "sessions"],
            "dimensions": ["date"],
            "date_range": "last7days",
            "limit": 3
        }
        
        result = await main_evolution_function(params)
        
        if "❌" not in result:
            print("✅ Relatório padrão funcionando")
            print("   Resultado:")
            print(result[:300] + "..." if len(result) > 300 else result)
        else:
            print(f"❌ Erro no relatório: {result}")
            return False
        
        # Teste de dados em tempo real
        params_realtime = {
            "property_id": "330715799",
            "action": "realtime",
            "metrics": ["activeUsers"],
            "limit": 3
        }
        
        result_realtime = await main_evolution_function(params_realtime)
        
        if "❌" not in result_realtime:
            print("✅ Dados em tempo real funcionando")
            print("   Resultado:")
            print(result_realtime[:200] + "..." if len(result_realtime) > 200 else result_realtime)
        else:
            print(f"❌ Erro nos dados em tempo real: {result_realtime}")
            return False
        
        # Teste de metadados
        params_metadata = {
            "property_id": "330715799",
            "action": "metadata",
            "metadata_type": "metrics"
        }
        
        result_metadata = await main_evolution_function(params_metadata)
        
        if "❌" not in result_metadata:
            print("✅ Metadados funcionando")
            print("   Resultado:")
            print(result_metadata[:300] + "..." if len(result_metadata) > 300 else result_metadata)
        else:
            print(f"❌ Erro nos metadados: {result_metadata}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Erro na integração direta: {e}")
        return False


def test_mcp_server():
    """Testa o servidor MCP nativo."""
    print("\n🧪 Testando servidor MCP nativo...")
    
    try:
        import subprocess
        import os
        
        # Testa se o comando mcp-server-ga4 está disponível
        result = subprocess.run(
            ["venv\\Scripts\\python", "-m", "mcp_server_ga4.main", "--help"],
            capture_output=True,
            text=True,
            timeout=10,
            cwd=os.getcwd()
        )
        
        if result.returncode == 0:
            print("✅ Comando mcp-server-ga4 disponível")
            print("   Opções disponíveis:")
            for line in result.stdout.split('\n')[:10]:
                if line.strip():
                    print(f"   {line}")
        else:
            print(f"❌ Erro no comando MCP: {result.stderr}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Erro no teste MCP: {e}")
        return False


def main():
    """Executa todos os testes."""
    print("🚀 Iniciando testes de integração GA4 Python MCP")
    print("=" * 60)
    
    # Teste 1: Integração direta
    success_direct = asyncio.run(test_direct_integration())
    
    # Teste 2: Servidor MCP
    success_mcp = test_mcp_server()
    
    # Teste 3: Servidor HTTP (se estiver rodando)
    success_http = test_http_server()
    
    # Resumo
    print("\n" + "=" * 60)
    print("📋 Resumo dos Testes:")
    print(f"   Integração Direta: {'✅ OK' if success_direct else '❌ FALHOU'}")
    print(f"   Servidor MCP:      {'✅ OK' if success_mcp else '❌ FALHOU'}")
    print(f"   Servidor HTTP:     {'✅ OK' if success_http else '❌ FALHOU'}")
    
    if success_direct and success_mcp:
        print("\n🎉 Sistema pronto para uso!")
        print("\n📋 Próximos passos:")
        print("   1. Use evolution_integration.py para integração direta")
        print("   2. Use http_server.py para servidor HTTP")
        print("   3. Use evolution_script.js na Evolution API")
        print("   4. Configure o Property ID conforme necessário")
    else:
        print("\n⚠️ Alguns testes falharam. Verifique a configuração.")
    
    return success_direct and success_mcp


if __name__ == "__main__":
    main()
