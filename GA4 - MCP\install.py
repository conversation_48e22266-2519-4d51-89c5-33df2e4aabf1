#!/usr/bin/env python3
"""
Script de instalação e configuração do GA4 MCP Server Python.
"""
import os
import subprocess
import sys
from pathlib import Path


def run_command(command, description):
    """Executa um comando e exibe o resultado."""
    print(f"\n🔄 {description}...")
    try:
        result = subprocess.run(
            command, shell=True, capture_output=True, text=True, check=True
        )
        print(f"✅ {description} concluído com sucesso")
        if result.stdout:
            print(f"   Output: {result.stdout.strip()}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Erro em {description}")
        print(f"   Erro: {e.stderr.strip()}")
        return False


def check_python_version():
    """Verifica se a versão do Python é compatível."""
    print("🔍 Verificando versão do Python...")
    version = sys.version_info
    if version.major == 3 and version.minor >= 9:
        print(f"✅ Python {version.major}.{version.minor}.{version.micro} é compatível")
        return True
    else:
        print(f"❌ Python {version.major}.{version.minor}.{version.micro} não é compatível")
        print("   Requer Python 3.9 ou superior")
        return False


def create_virtual_environment():
    """Cria um ambiente virtual Python."""
    venv_path = Path("venv")
    if venv_path.exists():
        print("✅ Ambiente virtual já existe")
        return True
    
    return run_command("python -m venv venv", "Criando ambiente virtual")


def activate_and_install():
    """Ativa o ambiente virtual e instala dependências."""
    # Determina o comando de ativação baseado no OS
    if os.name == 'nt':  # Windows
        activate_cmd = "venv\\Scripts\\activate"
        pip_cmd = "venv\\Scripts\\pip"
    else:  # Linux/Mac
        activate_cmd = "source venv/bin/activate"
        pip_cmd = "venv/bin/pip"
    
    # Atualiza pip
    if not run_command(f"{pip_cmd} install --upgrade pip", "Atualizando pip"):
        return False
    
    # Instala dependências básicas
    deps = [
        "mcp>=1.0.0",
        "google-analytics-data>=0.16.0", 
        "python-dotenv>=0.19.0"
    ]
    
    for dep in deps:
        if not run_command(f"{pip_cmd} install {dep}", f"Instalando {dep}"):
            return False
    
    # Instala o pacote em modo desenvolvimento
    return run_command(f"{pip_cmd} install -e .", "Instalando pacote em modo desenvolvimento")


def setup_environment():
    """Configura o arquivo .env."""
    env_file = Path(".env")
    env_example = Path(".env.example")
    
    if env_file.exists():
        print("✅ Arquivo .env já existe")
        return True
    
    if env_example.exists():
        try:
            content = env_example.read_text()
            env_file.write_text(content)
            print("✅ Arquivo .env criado a partir do .env.example")
            print("💡 Edite o arquivo .env com suas configurações")
            return True
        except Exception as e:
            print(f"❌ Erro ao criar .env: {e}")
            return False
    else:
        print("⚠️ Arquivo .env.example não encontrado")
        return False


def check_credentials():
    """Verifica se o arquivo de credenciais existe."""
    cred_file = Path("credenciais-ga4.json")
    if cred_file.exists():
        print("✅ Arquivo de credenciais encontrado")
        return True
    else:
        print("⚠️ Arquivo credenciais-ga4.json não encontrado")
        print("💡 Copie o arquivo de credenciais do diretório EVO - MCP")
        return False


def test_installation():
    """Testa a instalação."""
    print("\n🧪 Testando instalação...")
    
    # Determina o comando python baseado no OS
    if os.name == 'nt':  # Windows
        python_cmd = "venv\\Scripts\\python"
    else:  # Linux/Mac
        python_cmd = "venv/bin/python"
    
    # Testa importação dos módulos
    test_script = """
import sys
try:
    from mcp_server_ga4.main import create_server
    from mcp_server_ga4.ga4_client import GA4Client
    from mcp_server_ga4.tools import run_report, run_realtime_report, get_metadata
    print("✅ Todos os módulos importados com sucesso")
    sys.exit(0)
except ImportError as e:
    print(f"❌ Erro de importação: {e}")
    sys.exit(1)
"""
    
    try:
        result = subprocess.run(
            [python_cmd, "-c", test_script],
            capture_output=True,
            text=True,
            check=True
        )
        print(result.stdout.strip())
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Teste falhou: {e.stderr.strip()}")
        return False


def main():
    """Função principal de instalação."""
    print("🚀 Instalação do GA4 MCP Server Python")
    print("=" * 50)
    
    # Verifica versão do Python
    if not check_python_version():
        return False
    
    # Cria ambiente virtual
    if not create_virtual_environment():
        return False
    
    # Instala dependências
    if not activate_and_install():
        return False
    
    # Configura ambiente
    setup_environment()
    
    # Verifica credenciais
    check_credentials()
    
    # Testa instalação
    if not test_installation():
        return False
    
    print("\n🎉 Instalação concluída com sucesso!")
    print("\n📋 Próximos passos:")
    print("   1. Edite o arquivo .env com suas configurações")
    print("   2. Certifique-se de que credenciais-ga4.json está presente")
    print("   3. Execute: venv\\Scripts\\python -m mcp_server_ga4.main --help")
    print("   4. Para modo stdio: venv\\Scripts\\python -m mcp_server_ga4.main")
    print("   5. Para modo SSE: venv\\Scripts\\python -m mcp_server_ga4.main --transport sse")
    
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
