# GA4 Python MCP - Cloudflare Tunnel (PowerShell)

Write-Host "🚀 GA4 Python MCP - Cloudflare Tunnel" -ForegroundColor Green
Write-Host "=====================================" -ForegroundColor Green

# Muda para o diretório do script
Set-Location $PSScriptRoot

Write-Host "📋 Verificando ambiente..." -ForegroundColor Yellow

# Verifica ambiente virtual
if (-not (Test-Path "venv\Scripts\python.exe")) {
    Write-Host "❌ Ambiente virtual não encontrado" -ForegroundColor Red
    Write-Host "💡 Execute primeiro: python install.py" -ForegroundColor Cyan
    Read-Host "Pressione Enter para sair"
    exit 1
}

# Verifica credenciais
if (-not (Test-Path "credenciais-ga4.json")) {
    Write-Host "❌ Arquivo credenciais-ga4.json não encontrado" -ForegroundColor Red
    Write-Host "💡 Copie o arquivo de credenciais para este diretório" -ForegroundColor Cyan
    Read-Host "Pressione Enter para sair"
    exit 1
}

Write-Host "✅ Ambiente OK" -ForegroundColor Green
Write-Host ""

Write-Host "🌐 Iniciando túnel Cloudflare..." -ForegroundColor Yellow
Write-Host "⏳ Aguarde alguns segundos..." -ForegroundColor Yellow
Write-Host ""

try {
    & "venv\Scripts\python.exe" "cloudflare_tunnel.py" --port 8080
}
catch {
    Write-Host "❌ Erro ao executar túnel: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "🛑 Túnel encerrado" -ForegroundColor Yellow
Read-Host "Pressione Enter para sair"
