"""
Teste rápido para verificar se o túnel da última execução ainda funciona.
"""
import requests
import sys


def quick_test():
    """Teste rápido do túnel anterior."""
    # URL do túnel da última execução
    tunnel_url = "https://earl-mpegs-costa-qc.trycloudflare.com"
    
    print(f"🧪 Testando túnel anterior: {tunnel_url}")
    
    try:
        # Teste health check
        print("   Testando health check...")
        response = requests.get(f"{tunnel_url}/health", timeout=10)
        
        if response.status_code == 200:
            print("✅ Health check OK!")
            print(f"   Resposta: {response.json()}")
            
            # Teste endpoint GA4
            print("   Testando endpoint GA4...")
            ga4_response = requests.post(
                f"{tunnel_url}/ga4-evolution",
                json={
                    "property_id": "330715799",
                    "action": "report",
                    "metrics": ["activeUsers"],
                    "date_range": "last7days",
                    "limit": 3
                },
                timeout=20
            )
            
            if ga4_response.status_code == 200:
                result = ga4_response.json()
                if result.get('success'):
                    print("✅ Endpoint GA4 funcionando!")
                    print("📊 Dados obtidos:")
                    print(result['data'][:200] + "..." if len(result['data']) > 200 else result['data'])
                    
                    print(f"\n🎉 TÚNEL FUNCIONANDO!")
                    print(f"🌐 URL: {tunnel_url}")
                    print(f"📊 Endpoint: {tunnel_url}/ga4-evolution")
                    
                    print(f"\n💻 Use na Evolution API:")
                    print(f'   server_url: "{tunnel_url}"')
                    
                    return True
                else:
                    print(f"❌ Erro nos dados GA4: {result.get('error')}")
            else:
                print(f"❌ Endpoint GA4 retornou status {ga4_response.status_code}")
        else:
            print(f"❌ Health check retornou status {response.status_code}")
            
    except requests.exceptions.ConnectionError as e:
        if "NameResolutionError" in str(e):
            print("❌ Túnel não está mais ativo (DNS não resolve)")
        else:
            print(f"❌ Erro de conexão: {e}")
    except requests.exceptions.Timeout:
        print("❌ Timeout - túnel pode estar lento")
    except Exception as e:
        print(f"❌ Erro: {e}")
    
    print("\n💡 Para criar novo túnel:")
    print("   start_tunnel.bat")
    print("   ou: python start_tunnel_persistent.py")
    
    return False


if __name__ == "__main__":
    success = quick_test()
    
    if not success:
        print("\n🔄 Quer iniciar um novo túnel? (s/n)")
        try:
            choice = input().lower()
            if choice in ['s', 'y', 'sim', 'yes']:
                import subprocess
                print("🚀 Iniciando novo túnel...")
                subprocess.run([sys.executable, "start_tunnel_persistent.py"])
        except KeyboardInterrupt:
            pass
    
    input("\nPressione Enter para sair...")
    sys.exit(0 if success else 1)
