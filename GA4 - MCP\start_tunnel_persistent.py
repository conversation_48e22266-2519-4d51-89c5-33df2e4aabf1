"""
Script para iniciar o túnel Cloudflare de forma persistente.
Não para se o teste inicial falhar (comum com DNS).
"""
import asyncio
import json
import logging
import os
import subprocess
import sys
import time
from pathlib import Path

# Configuração de logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("persistent-tunnel")


class PersistentTunnel:
    """Túnel Cloudflare persistente que não para por problemas de DNS."""
    
    def __init__(self, local_port=8080):
        self.local_port = local_port
        self.tunnel_url = None
        self.tunnel_process = None
        self.server_process = None
        
    def start_server(self):
        """Inicia o servidor HTTP Python."""
        logger.info(f"🚀 Iniciando servidor HTTP na porta {self.local_port}...")
        
        try:
            cmd = [
                sys.executable,
                "http_server.py",
                "--port", str(self.local_port),
                "--host", "0.0.0.0"
            ]
            
            self.server_process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                cwd=os.getcwd()
            )
            
            # Aguarda servidor iniciar
            time.sleep(3)
            
            if self.server_process.poll() is None:
                logger.info("✅ Servidor HTTP iniciado com sucesso")
                return True
            else:
                logger.error("❌ Falha ao iniciar servidor HTTP")
                return False
                
        except Exception as e:
            logger.error(f"❌ Erro ao iniciar servidor: {e}")
            return False
    
    def start_tunnel(self):
        """Inicia o túnel Cloudflare."""
        logger.info("🌐 Iniciando túnel Cloudflare...")
        
        # Verifica cloudflared
        try:
            result = subprocess.run(
                ["cloudflared", "--version"],
                capture_output=True,
                text=True,
                timeout=10
            )
            if result.returncode == 0:
                logger.info(f"✅ Cloudflared encontrado: {result.stdout.strip()}")
                cloudflared_cmd = "cloudflared"
            else:
                raise FileNotFoundError()
        except:
            # Tenta usar cloudflared local
            cloudflared_path = Path("cloudflared.exe")
            if cloudflared_path.exists():
                cloudflared_cmd = str(cloudflared_path)
                logger.info("✅ Usando cloudflared local")
            else:
                logger.error("❌ Cloudflared não encontrado")
                return False
        
        try:
            cmd = [
                cloudflared_cmd,
                "tunnel",
                "--url", f"http://localhost:{self.local_port}",
                "--no-autoupdate"
            ]
            
            self.tunnel_process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            # Aguarda URL do túnel
            logger.info("⏳ Aguardando URL do túnel...")
            
            for _ in range(30):
                if self.tunnel_process.poll() is not None:
                    logger.error("❌ Processo do túnel terminou")
                    return False
                
                try:
                    line = self.tunnel_process.stderr.readline()
                    if line:
                        logger.debug(f"Cloudflared: {line.strip()}")
                        
                        if "trycloudflare.com" in line or "https://" in line:
                            parts = line.split()
                            for part in parts:
                                if part.startswith("https://") and "trycloudflare.com" in part:
                                    self.tunnel_url = part.strip()
                                    logger.info(f"✅ Túnel criado: {self.tunnel_url}")
                                    return True
                except:
                    pass
                
                time.sleep(1)
            
            logger.error("❌ Timeout ao aguardar URL do túnel")
            return False
            
        except Exception as e:
            logger.error(f"❌ Erro ao criar túnel: {e}")
            return False
    
    def save_tunnel_info(self):
        """Salva informações do túnel em arquivo."""
        if not self.tunnel_url:
            return
        
        info = {
            "tunnel_url": self.tunnel_url,
            "local_port": self.local_port,
            "health_endpoint": f"{self.tunnel_url}/health",
            "ga4_endpoint": f"{self.tunnel_url}/ga4-evolution",
            "status": "active",
            "started_at": time.strftime("%Y-%m-%dT%H:%M:%SZ")
        }
        
        try:
            with open("tunnel_info.json", "w") as f:
                json.dump(info, f, indent=2)
            logger.info("💾 Informações salvas em tunnel_info.json")
        except Exception as e:
            logger.error(f"❌ Erro ao salvar informações: {e}")
    
    def show_info(self):
        """Exibe informações do túnel."""
        if not self.tunnel_url:
            return
        
        print("\n" + "=" * 60)
        print("🎉 TÚNEL CLOUDFLARE ATIVO!")
        print("=" * 60)
        print(f"🌐 URL Pública: {self.tunnel_url}")
        print(f"🏠 Porta Local: {self.local_port}")
        print(f"❤️ Health Check: {self.tunnel_url}/health")
        print(f"📊 Endpoint GA4: {self.tunnel_url}/ga4-evolution")
        print("=" * 60)
        print("\n💻 Exemplo para Evolution API:")
        print("   const input = {")
        print(f'     server_url: "{self.tunnel_url}",')
        print('     property_id: "330715799",')
        print('     action: "report",')
        print('     metrics: ["activeUsers"]')
        print("   };")
        print("\n🧪 Teste manual:")
        print(f"   curl {self.tunnel_url}/health")
        print(f"   python test_tunnel.py")
        print("\n⚠️ IMPORTANTE:")
        print("   - O túnel pode demorar alguns minutos para funcionar (DNS)")
        print("   - Se der erro de DNS, aguarde e teste novamente")
        print("   - O túnel fica ativo enquanto este programa estiver rodando")
        print("=" * 60)
    
    def monitor_processes(self):
        """Monitora os processos e mantém rodando."""
        logger.info("\n⌨️ Pressione Ctrl+C para parar...")
        logger.info("🔄 Monitorando processos...")
        
        try:
            while True:
                # Verifica servidor
                if self.server_process and self.server_process.poll() is not None:
                    logger.error("❌ Servidor parou inesperadamente")
                    break
                
                # Verifica túnel
                if self.tunnel_process and self.tunnel_process.poll() is not None:
                    logger.error("❌ Túnel parou inesperadamente")
                    break
                
                time.sleep(2)
                
        except KeyboardInterrupt:
            logger.info("\n🛑 Parando por solicitação do usuário...")
    
    def stop(self):
        """Para todos os processos."""
        logger.info("🛑 Parando túnel e servidor...")
        
        if self.tunnel_process:
            self.tunnel_process.terminate()
            try:
                self.tunnel_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                self.tunnel_process.kill()
            logger.info("✅ Túnel parado")
        
        if self.server_process:
            self.server_process.terminate()
            try:
                self.server_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                self.server_process.kill()
            logger.info("✅ Servidor parado")
        
        # Remove arquivo de info
        try:
            if Path("tunnel_info.json").exists():
                os.remove("tunnel_info.json")
                logger.info("🗑️ Arquivo tunnel_info.json removido")
        except:
            pass
    
    def run(self):
        """Executa o sistema completo."""
        try:
            logger.info("🚀 Iniciando GA4 Python MCP com Cloudflare Tunnel (Persistente)")
            logger.info("=" * 70)
            
            # 1. Inicia servidor
            if not self.start_server():
                return False
            
            # 2. Inicia túnel
            if not self.start_tunnel():
                self.stop()
                return False
            
            # 3. Salva informações
            self.save_tunnel_info()
            
            # 4. Exibe informações
            self.show_info()
            
            # 5. Monitora (não para por erro de teste)
            self.monitor_processes()
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Erro geral: {e}")
            return False
        finally:
            self.stop()


def main():
    """Função principal."""
    import argparse
    
    parser = argparse.ArgumentParser(description="GA4 Python MCP - Túnel Persistente")
    parser.add_argument("--port", type=int, default=8080, help="Porta local")
    
    args = parser.parse_args()
    
    tunnel = PersistentTunnel(local_port=args.port)
    success = tunnel.run()
    
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
