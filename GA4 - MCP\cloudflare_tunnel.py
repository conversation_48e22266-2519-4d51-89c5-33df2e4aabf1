"""
Script para configurar e gerenciar túnel Cloudflare para o servidor GA4 Python MCP.
"""
import asyncio
import json
import logging
import os
import subprocess
import sys
import time
from pathlib import Path

# Configuração de logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("cloudflare-tunnel")


class CloudflareTunnel:
    """Gerenciador do túnel Cloudflare."""
    
    def __init__(self, local_port=8080):
        self.local_port = local_port
        self.tunnel_url = None
        self.tunnel_process = None
        self.server_process = None
        
    def check_cloudflared(self):
        """Verifica se cloudflared está instalado."""
        try:
            result = subprocess.run(
                ["cloudflared", "--version"],
                capture_output=True,
                text=True,
                timeout=10
            )
            if result.returncode == 0:
                logger.info(f"✅ Cloudflared encontrado: {result.stdout.strip()}")
                return True
            else:
                logger.error("❌ Cloudflared não está funcionando corretamente")
                return False
        except FileNotFoundError:
            logger.error("❌ Cloudflared não está instalado")
            return False
        except Exception as e:
            logger.error(f"❌ Erro ao verificar cloudflared: {e}")
            return False
    
    def install_cloudflared(self):
        """Instala cloudflared no Windows."""
        logger.info("📥 Instalando cloudflared...")
        
        try:
            # URL de download para Windows
            download_url = "https://github.com/cloudflare/cloudflared/releases/latest/download/cloudflared-windows-amd64.exe"
            
            # Baixa o arquivo
            import urllib.request
            cloudflared_path = Path("cloudflared.exe")
            
            logger.info("⬇️ Baixando cloudflared...")
            urllib.request.urlretrieve(download_url, cloudflared_path)
            
            # Testa se funciona
            result = subprocess.run(
                [str(cloudflared_path), "--version"],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            if result.returncode == 0:
                logger.info("✅ Cloudflared instalado com sucesso")
                return str(cloudflared_path)
            else:
                logger.error("❌ Falha ao instalar cloudflared")
                return None
                
        except Exception as e:
            logger.error(f"❌ Erro na instalação: {e}")
            return None
    
    def start_server(self):
        """Inicia o servidor HTTP Python."""
        logger.info(f"🚀 Iniciando servidor HTTP na porta {self.local_port}...")
        
        try:
            # Comando para iniciar o servidor
            cmd = [
                sys.executable,
                "http_server.py",
                "--port", str(self.local_port),
                "--host", "0.0.0.0"  # Aceita conexões de qualquer IP
            ]
            
            self.server_process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                cwd=os.getcwd()
            )
            
            # Aguarda um pouco para o servidor iniciar
            time.sleep(3)
            
            if self.server_process.poll() is None:
                logger.info("✅ Servidor HTTP iniciado com sucesso")
                return True
            else:
                logger.error("❌ Falha ao iniciar servidor HTTP")
                return False
                
        except Exception as e:
            logger.error(f"❌ Erro ao iniciar servidor: {e}")
            return False
    
    def start_tunnel(self):
        """Inicia o túnel Cloudflare."""
        logger.info("🌐 Iniciando túnel Cloudflare...")
        
        # Determina o comando cloudflared
        cloudflared_cmd = "cloudflared"
        if not self.check_cloudflared():
            cloudflared_path = self.install_cloudflared()
            if cloudflared_path:
                cloudflared_cmd = cloudflared_path
            else:
                return False
        
        try:
            # Comando para criar túnel
            cmd = [
                cloudflared_cmd,
                "tunnel",
                "--url", f"http://localhost:{self.local_port}",
                "--no-autoupdate"
            ]
            
            self.tunnel_process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            # Aguarda e captura a URL do túnel
            logger.info("⏳ Aguardando URL do túnel...")
            
            for _ in range(30):  # Aguarda até 30 segundos
                if self.tunnel_process.poll() is not None:
                    logger.error("❌ Processo do túnel terminou inesperadamente")
                    return False
                
                # Lê output do processo
                try:
                    line = self.tunnel_process.stderr.readline()
                    if line:
                        logger.debug(f"Cloudflared: {line.strip()}")
                        
                        # Procura pela URL do túnel
                        if "trycloudflare.com" in line or "https://" in line:
                            # Extrai a URL
                            parts = line.split()
                            for part in parts:
                                if part.startswith("https://") and "trycloudflare.com" in part:
                                    self.tunnel_url = part.strip()
                                    logger.info(f"✅ Túnel criado: {self.tunnel_url}")
                                    return True
                except:
                    pass
                
                time.sleep(1)
            
            logger.error("❌ Timeout ao aguardar URL do túnel")
            return False
            
        except Exception as e:
            logger.error(f"❌ Erro ao criar túnel: {e}")
            return False
    
    def test_tunnel(self):
        """Testa se o túnel está funcionando."""
        if not self.tunnel_url:
            logger.error("❌ URL do túnel não disponível")
            return False
        
        try:
            import requests
            
            logger.info("🧪 Testando túnel...")
            
            # Testa health check
            response = requests.get(f"{self.tunnel_url}/health", timeout=10)
            
            if response.status_code == 200:
                logger.info("✅ Túnel funcionando corretamente")
                logger.info(f"   Response: {response.json()}")
                return True
            else:
                logger.error(f"❌ Túnel retornou status {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Erro ao testar túnel: {e}")
            return False
    
    def get_tunnel_info(self):
        """Retorna informações do túnel."""
        if not self.tunnel_url:
            return None
        
        return {
            "tunnel_url": self.tunnel_url,
            "local_port": self.local_port,
            "health_endpoint": f"{self.tunnel_url}/health",
            "ga4_endpoint": f"{self.tunnel_url}/ga4-evolution",
            "status": "active" if self.tunnel_process and self.tunnel_process.poll() is None else "inactive"
        }
    
    def stop(self):
        """Para o túnel e o servidor."""
        logger.info("🛑 Parando túnel e servidor...")
        
        if self.tunnel_process:
            self.tunnel_process.terminate()
            try:
                self.tunnel_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                self.tunnel_process.kill()
            logger.info("✅ Túnel parado")
        
        if self.server_process:
            self.server_process.terminate()
            try:
                self.server_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                self.server_process.kill()
            logger.info("✅ Servidor parado")
    
    def run(self):
        """Executa o sistema completo (servidor + túnel)."""
        try:
            logger.info("🚀 Iniciando GA4 Python MCP com Cloudflare Tunnel")
            logger.info("=" * 60)
            
            # 1. Inicia servidor
            if not self.start_server():
                return False
            
            # 2. Inicia túnel
            if not self.start_tunnel():
                self.stop()
                return False
            
            # 3. Testa túnel
            if not self.test_tunnel():
                self.stop()
                return False
            
            # 4. Exibe informações
            info = self.get_tunnel_info()
            logger.info("\n🎉 Sistema iniciado com sucesso!")
            logger.info("=" * 60)
            logger.info(f"🌐 URL Pública: {info['tunnel_url']}")
            logger.info(f"🏠 Porta Local: {info['local_port']}")
            logger.info(f"❤️ Health Check: {info['health_endpoint']}")
            logger.info(f"📊 Endpoint GA4: {info['ga4_endpoint']}")
            logger.info("=" * 60)
            
            # Salva informações em arquivo
            with open("tunnel_info.json", "w") as f:
                json.dump(info, f, indent=2)
            logger.info("💾 Informações salvas em tunnel_info.json")
            
            # 5. Mantém rodando
            logger.info("\n⌨️ Pressione Ctrl+C para parar...")
            try:
                while True:
                    if self.tunnel_process.poll() is not None:
                        logger.error("❌ Túnel parou inesperadamente")
                        break
                    if self.server_process.poll() is not None:
                        logger.error("❌ Servidor parou inesperadamente")
                        break
                    time.sleep(1)
            except KeyboardInterrupt:
                logger.info("\n🛑 Parando por solicitação do usuário...")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Erro geral: {e}")
            return False
        finally:
            self.stop()


def main():
    """Função principal."""
    import argparse
    
    parser = argparse.ArgumentParser(description="GA4 Python MCP com Cloudflare Tunnel")
    parser.add_argument("--port", type=int, default=8080, help="Porta local do servidor")
    parser.add_argument("--test-only", action="store_true", help="Apenas testa se cloudflared está disponível")
    
    args = parser.parse_args()
    
    tunnel = CloudflareTunnel(local_port=args.port)
    
    if args.test_only:
        if tunnel.check_cloudflared():
            print("✅ Cloudflared está disponível")
            sys.exit(0)
        else:
            print("❌ Cloudflared não está disponível")
            sys.exit(1)
    
    success = tunnel.run()
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
