# GA4 MCP Server Python

Sistema MCP (Model Context Protocol) em Python para integração avançada com Google Analytics 4.

## 🎯 Objetivo

Este diretório contém o servidor MCP Python que complementa o sistema Node.js existente, oferecendo:

- **Protocolo MCP nativo** para integração com LLMs
- **Ferramentas avançadas** de análise GA4
- **Formatação automática** de relatórios
- **Suporte a tempo real** e metadados

## 🏗️ Arquitetura Híbrida

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Evolution     │    │   Node.js MCP   │    │  Python MCP     │
│     API         │◄──►│    Server       │◄──►│    Server       │
│                 │    │  (Port 3000)    │    │  (stdio/SSE)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 📦 Instalação

### 1. <PERSON><PERSON>r Ambiente Virtual Python

```bash
cd "GA4 - MCP"
python -m venv venv
venv\Scripts\activate  # Windows
# source venv/bin/activate  # Linux/Mac
```

### 2. Instalar Dependências

```bash
pip install mcp-server-ga4
# ou para desenvolvimento:
pip install -e .
```

### 3. Configurar Credenciais

Copie o arquivo de credenciais do diretório EVO - MCP:

```bash
copy "..\EVO - MCP\credenciais-ga4.json" "credenciais-ga4.json"
```

### 4. Configurar Variáveis de Ambiente

```bash
# Arquivo .env
GA4_PROPERTY_ID=330715799
GOOGLE_APPLICATION_CREDENTIALS=credenciais-ga4.json
```

## 🚀 Uso

### Modo Stdio (Para LLMs)

```bash
mcp-server-ga4 --property-id 330715799
```

### Modo SSE (Para Web)

```bash
mcp-server-ga4 --transport sse --port 8001 --property-id 330715799
```

## 🔧 Ferramentas Disponíveis

### 1. run-report
Executa relatórios GA4 padrão com dimensões e métricas personalizáveis.

### 2. run-realtime-report
Obtém dados em tempo real dos últimos 30 minutos.

### 3. get-metadata
Recupera metadados sobre métricas e dimensões disponíveis.

## 🔗 Integração com Sistema Node.js

O servidor Python MCP pode ser usado em conjunto com o servidor Node.js:

1. **Node.js** para Evolution API (compatibilidade)
2. **Python MCP** para LLMs e análises avançadas
3. **Bridge** para comunicação entre sistemas

## 📋 Próximos Passos

1. Instalar o servidor Python MCP
2. Configurar credenciais
3. Testar integração
4. Criar bridge de comunicação
5. Atualizar scripts Evolution API
