# GA4 MCP Server Python - Solução Completa

Sistema MCP (Model Context Protocol) em Python para integração completa com Google Analytics 4 e Evolution API.

## 🎯 Objetivo

Este sistema **substitui completamente** o servidor Node.js, oferecendo uma solução unificada em Python:

- **Integração direta** com Evolution API
- **Servidor HTTP** para requisições REST
- **Protocolo MCP nativo** para LLMs
- **Ferramentas avançadas** de análise GA4
- **Formatação automática** de relatórios
- **Suporte a tempo real** e metadados
- **Fallback automático** para alta disponibilidade

## 🏗️ Arquitetura Simplificada

```
┌─────────────────┐    ┌─────────────────────────────────┐
│   Evolution     │    │        GA4 Python MCP           │
│     API         │◄──►│                                 │
│                 │    │  ┌─────────────┐ ┌─────────────┐ │
│                 │    │  │ HTTP Server │ │ MCP Server  │ │
│                 │    │  │ Port 8080   │ │ stdio/SSE   │ │
│                 │    │  └─────────────┘ └─────────────┘ │
└─────────────────┘    └─────────────────────────────────┘
```

## 📦 Instalação Rápida

### Opção 1: Instalação Automática

```bash
cd "GA4 - MCP"
python install.py
```

### Opção 2: Instalação Manual

```bash
cd "GA4 - MCP"
python -m venv venv
venv\Scripts\activate  # Windows
pip install -e .
copy "..\EVO - MCP\credenciais-ga4.json" "credenciais-ga4.json"
copy ".env.example" ".env"
```

### 3. Verificar Instalação

```bash
python test_integration.py
```

## 🚀 Modos de Uso

### 1. Evolution API (Recomendado)

Use o script `evolution_final.js` na Evolution API:

```javascript
// Parâmetros de entrada
const input = {
  property_id: "*********",
  action: "report",           // "report", "realtime", "metadata"
  metrics: ["activeUsers", "sessions"],
  dimensions: ["date", "country"],
  date_range: "last7days",    // "today", "yesterday", "last7days", "last30days"
  limit: 10
};
```

### 2. Servidor HTTP Local

```bash
# Iniciar servidor local
python http_server.py --port 8080

# Fazer requisições
curl -X POST http://localhost:8080/ga4-evolution \
  -H "Content-Type: application/json" \
  -d '{"property_id": "*********", "action": "report", "metrics": ["activeUsers"]}'
```

### 3. Servidor HTTP com Túnel Cloudflare (Recomendado para Evolution API)

```bash
# Iniciar servidor + túnel público
python cloudflare_tunnel.py

# Ou usar scripts prontos
start_tunnel.bat          # Windows
.\start_tunnel.ps1        # PowerShell

# Resultado: URL pública como https://abc123.trycloudflare.com
```

**Vantagens do túnel:**
- ✅ **Acesso público** - Evolution API pode acessar de qualquer lugar
- ✅ **HTTPS automático** - Certificado SSL gratuito
- ✅ **Sem configuração de rede** - Não precisa abrir portas
- ✅ **Proteção Cloudflare** - DDoS protection incluído

### 4. Integração Direta Python

```python
from evolution_integration import main_evolution_function
import asyncio

params = {
    "property_id": "*********",
    "action": "report",
    "metrics": ["activeUsers"],
    "date_range": "last7days"
}

result = asyncio.run(main_evolution_function(params))
print(result)
```

### 5. Servidor MCP Nativo (Para LLMs)

```bash
# Modo stdio
venv\Scripts\python -m mcp_server_ga4.main --property-id *********

# Modo SSE
venv\Scripts\python -m mcp_server_ga4.main --transport sse --port 8001
```

## 🔧 Funcionalidades

### Ações Disponíveis

| Ação | Descrição | Parâmetros |
|------|-----------|------------|
| `report` | Relatórios padrão GA4 | metrics, dimensions, date_range, limit |
| `realtime` | Dados em tempo real | metrics, dimensions, limit |
| `metadata` | Metadados disponíveis | metadata_type ("metrics", "dimensions", "all") |

### Métricas Populares

- `activeUsers` - Usuários ativos
- `sessions` - Sessões
- `pageviews` - Visualizações de página
- `bounceRate` - Taxa de rejeição
- `sessionDuration` - Duração da sessão

### Dimensões Populares

- `date` - Data
- `country` - País
- `city` - Cidade
- `deviceCategory` - Categoria do dispositivo
- `browser` - Navegador
- `operatingSystem` - Sistema operacional

## 📊 Exemplos de Uso

### Relatório Básico

```json
{
  "property_id": "*********",
  "action": "report",
  "metrics": ["activeUsers", "sessions"],
  "dimensions": ["date"],
  "date_range": "last7days",
  "limit": 10
}
```

### Dados em Tempo Real

```json
{
  "property_id": "*********",
  "action": "realtime",
  "metrics": ["activeUsers"],
  "dimensions": ["country"],
  "limit": 5
}
```

### Obter Metadados

```json
{
  "property_id": "*********",
  "action": "metadata",
  "metadata_type": "metrics"
}
```

## 🧪 Testes e Validação

### Executar Todos os Testes

```bash
python test_integration.py
```

### Testes Individuais

```bash
# Teste de integração direta
python evolution_integration.py

# Teste do servidor HTTP
python http_server.py --port 8080
# Em outro terminal:
curl http://localhost:8080/health

# Teste do servidor MCP
venv\Scripts\python -m mcp_server_ga4.main --help
```

## 🔒 Segurança e Configuração

### Credenciais

- Arquivo `credenciais-ga4.json` deve estar presente
- Variável `GOOGLE_APPLICATION_CREDENTIALS` configurada
- Property ID válido do GA4

### Variáveis de Ambiente

```bash
# .env
GA4_PROPERTY_ID=*********
GOOGLE_APPLICATION_CREDENTIALS=credenciais-ga4.json
DEBUG=false
```

## 🚨 Solução de Problemas

### Erro: "Credenciais não encontradas"

```bash
# Verifique se o arquivo existe
ls credenciais-ga4.json

# Configure a variável de ambiente
set GOOGLE_APPLICATION_CREDENTIALS=credenciais-ga4.json
```

### Erro: "Property ID inválido"

1. Verifique o Property ID no Google Analytics
2. Use apenas números (ex: *********)
3. Confirme as permissões da Service Account

### Erro: "Servidor não responde"

```bash
# Verifique se o servidor está rodando
curl http://localhost:8080/health

# Reinicie o servidor
python http_server.py --port 8080
```

## 📁 Estrutura de Arquivos

```
GA4 - MCP/
├── mcp_server_ga4/           # Pacote principal
│   ├── __init__.py
│   ├── main.py               # Servidor MCP nativo
│   ├── ga4_client.py         # Cliente GA4
│   ├── tools.py              # Ferramentas MCP
│   └── mcp_simple.py         # Implementação simplificada
├── evolution_integration.py  # Integração direta Python
├── evolution_final.js        # Script para Evolution API
├── http_server.py            # Servidor HTTP
├── test_integration.py       # Testes completos
├── install.py                # Instalação automática
├── credenciais-ga4.json      # Credenciais GA4
├── .env                      # Variáveis de ambiente
├── pyproject.toml            # Configuração Python
└── README.md                 # Esta documentação
```

## 🎉 Conclusão

Este sistema oferece uma solução completa e robusta para integração GA4 com Evolution API:

✅ **Instalação simples** com script automatizado
✅ **Múltiplos modos** de uso (HTTP, MCP, direto)
✅ **Fallback automático** para alta disponibilidade
✅ **Testes abrangentes** para validação
✅ **Documentação completa** com exemplos
✅ **Compatibilidade total** com Evolution API

### Próximos Passos

1. ✅ Execute `python install.py` para instalação
2. ✅ Execute `python test_integration.py` para validação
3. ✅ Use `evolution_final.js` na Evolution API
4. ✅ Configure o Property ID conforme necessário
5. ✅ Monitore logs para troubleshooting

**🚀 Sistema pronto para produção!**
