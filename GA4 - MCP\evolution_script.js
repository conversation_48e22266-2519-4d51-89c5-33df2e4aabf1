/**
 * Script para Evolution API - Integração GA4 via Python MCP
 * 
 * Este script substitui completamente o servidor Node.js e usa
 * diretamente o servidor Python MCP para obter dados do GA4.
 * 
 * @param {Object} input - Parâmetros de entrada
 * @param {string} input.property_id - ID da propriedade GA4 (padrão: "330715799")
 * @param {string} input.action - Ação a executar ("report", "realtime", "metadata")
 * @param {Array} input.metrics - Métricas GA4 (ex: ["activeUsers", "sessions"])
 * @param {Array} input.dimensions - Dimensões GA4 (ex: ["date", "country"])
 * @param {string} input.date_range - Período ("last7days", "last30days", "today", "yesterday")
 * @param {number} input.limit - Número máximo de registros (padrão: 10)
 * @param {string} input.metadata_type - Tipo de metadados ("metrics", "dimensions", "all")
 * @param {Object} context - Contexto da Evolution (não usado nesta versão)
 */
module.exports = async function(input, context) {
  const { spawn } = require('child_process');
  const path = require('path');
  
  try {
    console.log('🚀 Iniciando integração GA4 via Python MCP');
    
    // Configurações padrão
    const config = {
      property_id: input.property_id || "330715799",
      action: input.action || "report",
      metrics: input.metrics || ["activeUsers"],
      dimensions: input.dimensions || null,
      date_range: input.date_range || "last30days",
      limit: input.limit || 10,
      metadata_type: input.metadata_type || "all"
    };
    
    console.log(`📊 Configuração: ${JSON.stringify(config, null, 2)}`);
    
    // Caminho para o script Python
    const pythonScript = path.join(__dirname, 'evolution_integration.py');
    const pythonExecutable = path.join(__dirname, 'venv', 'Scripts', 'python.exe');
    
    // Executa o script Python
    const result = await executePythonScript(pythonExecutable, pythonScript, config);
    
    console.log('✅ Dados obtidos com sucesso');
    
    return {
      success: true,
      data: result,
      source: "python-mcp-evolution",
      timestamp: new Date().toISOString(),
      config: config
    };
    
  } catch (error) {
    console.error('❌ Erro na integração:', error);
    
    return {
      success: false,
      error: error.message,
      source: "python-mcp-evolution",
      timestamp: new Date().toISOString()
    };
  }
};

/**
 * Executa o script Python e retorna o resultado
 */
function executePythonScript(pythonPath, scriptPath, config) {
  return new Promise((resolve, reject) => {
    // Prepara o código Python para execução
    const pythonCode = `
import asyncio
import json
import sys
import os

# Adiciona o diretório atual ao path
sys.path.insert(0, '${__dirname.replace(/\\/g, '\\\\')}')

from evolution_integration import main_evolution_function

# Parâmetros de entrada
input_params = ${JSON.stringify(config)}

# Executa a função principal
result = asyncio.run(main_evolution_function(input_params))
print(result)
`;
    
    const python = spawn(pythonPath, ['-c', pythonCode], {
      cwd: __dirname,
      env: {
        ...process.env,
        GOOGLE_APPLICATION_CREDENTIALS: path.join(__dirname, 'credenciais-ga4.json'),
        GA4_PROPERTY_ID: config.property_id
      }
    });
    
    let output = '';
    let errorOutput = '';
    
    python.stdout.on('data', (data) => {
      output += data.toString();
    });
    
    python.stderr.on('data', (data) => {
      errorOutput += data.toString();
    });
    
    python.on('close', (code) => {
      if (code === 0) {
        resolve(output.trim());
      } else {
        reject(new Error(`Python script failed with code ${code}: ${errorOutput}`));
      }
    });
    
    python.on('error', (error) => {
      reject(new Error(`Failed to start Python process: ${error.message}`));
    });
  });
}

/**
 * Função de teste para desenvolvimento
 */
async function testFunction() {
  const testInput = {
    property_id: "330715799",
    action: "report",
    metrics: ["activeUsers", "sessions"],
    dimensions: ["date"],
    date_range: "last7days",
    limit: 5
  };
  
  const result = await module.exports(testInput, {});
  console.log('Resultado do teste:', JSON.stringify(result, null, 2));
}

// Executa teste se chamado diretamente
if (require.main === module) {
  testFunction().catch(console.error);
}
