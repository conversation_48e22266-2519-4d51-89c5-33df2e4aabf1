"""
Script de integração para Evolution API usando o servidor Python MCP GA4.
Este script substitui completamente o servidor Node.js.
"""
import asyncio
import json
import logging
import os
import sys
from typing import Any, Dict, List, Optional

# Configuração de logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("evolution-ga4")


async def get_ga4_data(
    property_id: str,
    metrics: List[str] = None,
    dimensions: Optional[List[str]] = None,
    date_range: str = "last30days",
    limit: int = 10,
    realtime: bool = False
) -> Dict[str, Any]:
    """
    Obtém dados do GA4 usando o cliente Python diretamente.
    
    Args:
        property_id: ID da propriedade GA4
        metrics: Lista de métricas (ex: ["activeUsers", "sessions"])
        dimensions: Lista de dimensões (ex: ["date", "country"])
        date_range: Período dos dados ("last7days", "last30days", "today", "yesterday")
        limit: Número máximo de linhas
        realtime: Se True, obtém dados em tempo real
    
    Returns:
        Dados formatados do GA4
    """
    try:
        from mcp_server_ga4.ga4_client import GA4Client
        
        # Valores padrão
        if metrics is None:
            metrics = ["activeUsers"]
        
        logger.info(f"🔄 Iniciando consulta GA4 para propriedade: {property_id}")
        logger.info(f"   Métricas: {metrics}")
        logger.info(f"   Dimensões: {dimensions}")
        logger.info(f"   Período: {date_range}")
        logger.info(f"   Tempo real: {realtime}")
        
        # Cria cliente GA4
        client = GA4Client(default_property_id=property_id)
        
        try:
            # Verifica autenticação
            await client.verify_auth()
            logger.info("✅ Autenticação GA4 verificada")
            
            # Executa consulta
            if realtime:
                result = await client.run_realtime_report(
                    property_id=property_id,
                    metrics=metrics,
                    dimensions=dimensions,
                    limit=limit
                )
            else:
                result = await client.run_report(
                    property_id=property_id,
                    metrics=metrics,
                    dimensions=dimensions,
                    date_range=date_range,
                    limit=limit
                )
            
            logger.info(f"✅ Dados obtidos: {result['row_count']} registros")
            
            return {
                "success": True,
                "data": result,
                "source": "python-mcp",
                "timestamp": "2024-01-01T12:00:00.000Z",
                "config": {
                    "property_id": property_id,
                    "metrics": metrics,
                    "dimensions": dimensions,
                    "date_range": date_range,
                    "realtime": realtime
                }
            }
            
        finally:
            await client.close()
            
    except Exception as e:
        logger.error(f"❌ Erro ao obter dados GA4: {e}")
        return {
            "success": False,
            "error": str(e),
            "source": "python-mcp",
            "timestamp": "2024-01-01T12:00:00.000Z"
        }


async def get_ga4_metadata(property_id: str, metadata_type: str = "all") -> Dict[str, Any]:
    """
    Obtém metadados do GA4 (métricas e dimensões disponíveis).
    
    Args:
        property_id: ID da propriedade GA4
        metadata_type: Tipo de metadados ("metrics", "dimensions", "all")
    
    Returns:
        Metadados formatados
    """
    try:
        from mcp_server_ga4.ga4_client import GA4Client
        
        logger.info(f"🔄 Obtendo metadados GA4 para propriedade: {property_id}")
        
        client = GA4Client(default_property_id=property_id)
        
        try:
            await client.verify_auth()
            
            result = await client.get_metadata(
                property_id=property_id,
                metadata_type=metadata_type
            )
            
            logger.info(f"✅ Metadados obtidos: {metadata_type}")
            
            return {
                "success": True,
                "data": result,
                "source": "python-mcp",
                "timestamp": "2024-01-01T12:00:00.000Z"
            }
            
        finally:
            await client.close()
            
    except Exception as e:
        logger.error(f"❌ Erro ao obter metadados: {e}")
        return {
            "success": False,
            "error": str(e),
            "source": "python-mcp",
            "timestamp": "2024-01-01T12:00:00.000Z"
        }


def format_for_evolution(data: Dict[str, Any]) -> str:
    """
    Formata os dados para exibição na Evolution API.
    
    Args:
        data: Dados retornados pelas funções GA4
    
    Returns:
        String formatada para exibição
    """
    if not data.get("success"):
        return f"❌ Erro: {data.get('error', 'Erro desconhecido')}"
    
    result = data.get("data", {})
    
    if "rows" in result:
        # Dados de relatório
        rows = result["rows"]
        if not rows:
            return "📊 Nenhum dado encontrado para o período especificado."
        
        # Cabeçalhos
        dimensions = result.get("dimensions", [])
        metrics = result.get("metrics", [])
        headers = dimensions + metrics
        
        # Formata como tabela
        lines = ["📊 **Relatório GA4**\n"]
        
        # Linha de cabeçalho
        header_line = "| " + " | ".join(headers) + " |"
        separator = "| " + " | ".join(["---"] * len(headers)) + " |"
        lines.extend([header_line, separator])
        
        # Linhas de dados
        for row in rows[:10]:  # Limita a 10 linhas para não sobrecarregar
            values = []
            for header in headers:
                values.append(str(row.get(header, "")))
            lines.append("| " + " | ".join(values) + " |")
        
        if len(rows) > 10:
            lines.append(f"\n... e mais {len(rows) - 10} registros")
        
        # Totais se disponíveis
        if result.get("totals"):
            lines.append("\n**Totais:**")
            for total in result["totals"]:
                total_values = []
                for header in headers:
                    if header in dimensions:
                        total_values.append("")
                    else:
                        total_values.append(str(total.get(header, "")))
                lines.append("| " + " | ".join(total_values) + " |")
        
        return "\n".join(lines)
    
    elif "metrics" in result or "dimensions" in result:
        # Metadados
        lines = ["📋 **Metadados GA4**\n"]
        
        if "metrics" in result:
            lines.append("## Métricas Disponíveis")
            for metric in result["metrics"][:20]:  # Limita para não sobrecarregar
                lines.append(f"- **{metric['name']}**: {metric['display_name']}")
                if metric.get("description"):
                    lines.append(f"  - {metric['description']}")
            
            if len(result["metrics"]) > 20:
                lines.append(f"\n... e mais {len(result['metrics']) - 20} métricas")
        
        if "dimensions" in result:
            lines.append("\n## Dimensões Disponíveis")
            for dimension in result["dimensions"][:20]:  # Limita para não sobrecarregar
                lines.append(f"- **{dimension['name']}**: {dimension['display_name']}")
                if dimension.get("description"):
                    lines.append(f"  - {dimension['description']}")
            
            if len(result["dimensions"]) > 20:
                lines.append(f"\n... e mais {len(result['dimensions']) - 20} dimensões")
        
        return "\n".join(lines)
    
    else:
        return f"✅ Operação concluída: {json.dumps(result, indent=2)}"


# Função principal para uso na Evolution API
async def main_evolution_function(input_params: Dict[str, Any]) -> str:
    """
    Função principal para ser chamada pela Evolution API.
    
    Args:
        input_params: Parâmetros de entrada da Evolution
        
    Returns:
        Resultado formatado para exibição
    """
    try:
        # Extrai parâmetros
        property_id = input_params.get("property_id", "330715799")
        action = input_params.get("action", "report")  # "report", "realtime", "metadata"
        metrics = input_params.get("metrics", ["activeUsers"])
        dimensions = input_params.get("dimensions")
        date_range = input_params.get("date_range", "last30days")
        limit = input_params.get("limit", 10)
        
        logger.info(f"🚀 Executando ação: {action}")
        
        if action == "metadata":
            metadata_type = input_params.get("metadata_type", "all")
            result = await get_ga4_metadata(property_id, metadata_type)
        elif action == "realtime":
            result = await get_ga4_data(
                property_id=property_id,
                metrics=metrics,
                dimensions=dimensions,
                limit=limit,
                realtime=True
            )
        else:  # action == "report"
            result = await get_ga4_data(
                property_id=property_id,
                metrics=metrics,
                dimensions=dimensions,
                date_range=date_range,
                limit=limit,
                realtime=False
            )
        
        return format_for_evolution(result)
        
    except Exception as e:
        logger.error(f"❌ Erro na função principal: {e}")
        return f"❌ Erro: {str(e)}"


# Para teste direto
if __name__ == "__main__":
    # Exemplo de uso
    test_params = {
        "property_id": "330715799",
        "action": "report",
        "metrics": ["activeUsers", "sessions"],
        "dimensions": ["date"],
        "date_range": "last7days",
        "limit": 5
    }
    
    result = asyncio.run(main_evolution_function(test_params))
    print(result)
