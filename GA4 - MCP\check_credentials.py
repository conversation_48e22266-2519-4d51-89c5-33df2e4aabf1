"""
Script para verificar se as credenciais GA4 estão configuradas corretamente.
"""
import json
import os
from pathlib import Path


def check_credentials_file():
    """Verifica o arquivo de credenciais."""
    print("🔑 Verificando arquivo de credenciais...")
    
    cred_file = Path("credenciais-ga4.json")
    
    if not cred_file.exists():
        print("❌ Arquivo credenciais-ga4.json não encontrado")
        print("💡 Você precisa:")
        print("   1. Copiar o arquivo do diretório EVO - MCP")
        print("   2. Ou baixar do Google Cloud Console")
        return False
    
    try:
        with open(cred_file, 'r') as f:
            creds = json.load(f)
        
        # Verifica campos obrigatórios
        required_fields = [
            'type', 'project_id', 'private_key_id', 'private_key',
            'client_email', 'client_id', 'auth_uri', 'token_uri'
        ]
        
        missing_fields = []
        for field in required_fields:
            if field not in creds:
                missing_fields.append(field)
        
        if missing_fields:
            print(f"❌ Campos obrigatórios ausentes: {missing_fields}")
            return False
        
        print("✅ Arquivo de credenciais válido")
        print(f"   Tipo: {creds.get('type')}")
        print(f"   Projeto: {creds.get('project_id')}")
        print(f"   Email: {creds.get('client_email')}")
        
        return True
        
    except json.JSONDecodeError:
        print("❌ Arquivo de credenciais não é JSON válido")
        return False
    except Exception as e:
        print(f"❌ Erro ao ler credenciais: {e}")
        return False


def check_env_file():
    """Verifica o arquivo .env."""
    print("\n📋 Verificando arquivo .env...")
    
    env_file = Path(".env")
    
    if not env_file.exists():
        print("⚠️ Arquivo .env não encontrado")
        print("💡 Criando arquivo .env padrão...")
        
        try:
            with open(env_file, 'w') as f:
                f.write("# Configuração do GA4 MCP Server\n")
                f.write("GA4_PROPERTY_ID=*********\n")
                f.write("GOOGLE_APPLICATION_CREDENTIALS=credenciais-ga4.json\n")
                f.write("\n# Configurações opcionais\n")
                f.write("NODE_ENV=development\n")
                f.write("DEBUG=true\n")
            
            print("✅ Arquivo .env criado")
            return True
            
        except Exception as e:
            print(f"❌ Erro ao criar .env: {e}")
            return False
    
    try:
        with open(env_file, 'r') as f:
            content = f.read()
        
        print("✅ Arquivo .env encontrado")
        
        # Verifica configurações importantes
        if "GA4_PROPERTY_ID" in content:
            print("   ✅ GA4_PROPERTY_ID configurado")
        else:
            print("   ⚠️ GA4_PROPERTY_ID não encontrado")
        
        if "GOOGLE_APPLICATION_CREDENTIALS" in content:
            print("   ✅ GOOGLE_APPLICATION_CREDENTIALS configurado")
        else:
            print("   ⚠️ GOOGLE_APPLICATION_CREDENTIALS não encontrado")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro ao ler .env: {e}")
        return False


def test_authentication():
    """Testa a autenticação com GA4."""
    print("\n🧪 Testando autenticação GA4...")
    
    try:
        # Configura variáveis de ambiente
        os.environ["GOOGLE_APPLICATION_CREDENTIALS"] = "credenciais-ga4.json"
        os.environ["GA4_PROPERTY_ID"] = "*********"
        
        # Importa e testa cliente
        from mcp_server_ga4.ga4_client import GA4Client
        import asyncio
        
        async def test_auth():
            client = GA4Client(default_property_id="*********")
            try:
                await client.verify_auth()
                print("✅ Autenticação GA4 bem-sucedida")
                return True
            except Exception as e:
                print(f"❌ Erro na autenticação: {e}")
                return False
            finally:
                await client.close()
        
        return asyncio.run(test_auth())
        
    except ImportError as e:
        print(f"❌ Erro de importação: {e}")
        print("💡 Execute: python install.py")
        return False
    except Exception as e:
        print(f"❌ Erro no teste: {e}")
        return False


def check_permissions():
    """Verifica permissões da Service Account."""
    print("\n🔐 Verificando permissões...")
    
    try:
        with open("credenciais-ga4.json", 'r') as f:
            creds = json.load(f)
        
        email = creds.get('client_email', 'N/A')
        project = creds.get('project_id', 'N/A')
        
        print(f"📧 Service Account: {email}")
        print(f"📁 Projeto: {project}")
        print("\n💡 Permissões necessárias no Google Analytics:")
        print("   ✅ Viewer (Visualizador) - mínimo")
        print("   ✅ Analyst (Analista) - recomendado")
        print("   ✅ Acesso à propriedade *********")
        
        print("\n🔗 Para verificar/configurar permissões:")
        print("   1. Acesse: https://analytics.google.com")
        print("   2. Vá em Admin > Gerenciamento de usuários")
        print(f"   3. Adicione: {email}")
        print("   4. Conceda permissão 'Viewer' ou 'Analyst'")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro ao verificar permissões: {e}")
        return False


def show_summary(creds_ok, env_ok, auth_ok):
    """Mostra resumo da verificação."""
    print("\n" + "=" * 60)
    print("📋 RESUMO DA VERIFICAÇÃO")
    print("=" * 60)
    print(f"🔑 Arquivo credenciais: {'✅ OK' if creds_ok else '❌ PROBLEMA'}")
    print(f"📋 Arquivo .env:        {'✅ OK' if env_ok else '❌ PROBLEMA'}")
    print(f"🧪 Autenticação GA4:    {'✅ OK' if auth_ok else '❌ PROBLEMA'}")
    print("=" * 60)
    
    if creds_ok and env_ok and auth_ok:
        print("🎉 TUDO CONFIGURADO CORRETAMENTE!")
        print("\n🚀 Próximos passos:")
        print("   1. Execute: start_tunnel.bat")
        print("   2. Use a URL gerada na Evolution API")
        print("   3. Configure o Property ID conforme necessário")
    else:
        print("⚠️ PROBLEMAS ENCONTRADOS")
        print("\n🔧 Soluções:")
        
        if not creds_ok:
            print("   📁 Credenciais:")
            print("      - Copie credenciais-ga4.json do diretório EVO - MCP")
            print("      - Ou baixe nova Service Account do Google Cloud")
        
        if not env_ok:
            print("   📋 Configuração:")
            print("      - Execute: copy .env.example .env")
            print("      - Edite o arquivo .env conforme necessário")
        
        if not auth_ok:
            print("   🔐 Autenticação:")
            print("      - Verifique permissões no Google Analytics")
            print("      - Confirme se Service Account tem acesso à propriedade")
            print("      - Execute: python install.py")


def main():
    """Função principal."""
    print("🔍 VERIFICAÇÃO DE CREDENCIAIS GA4")
    print("=" * 60)
    
    # Verifica arquivos
    creds_ok = check_credentials_file()
    env_ok = check_env_file()
    
    # Testa autenticação (só se arquivos estão OK)
    auth_ok = False
    if creds_ok:
        auth_ok = test_authentication()
        if creds_ok and auth_ok:
            check_permissions()
    
    # Mostra resumo
    show_summary(creds_ok, env_ok, auth_ok)
    
    return creds_ok and env_ok and auth_ok


if __name__ == "__main__":
    success = main()
    
    print(f"\n💡 Para mais informações, consulte: README.md")
    input("\nPressione Enter para sair...")
    
    import sys
    sys.exit(0 if success else 1)
