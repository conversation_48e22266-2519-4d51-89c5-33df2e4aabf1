# GA4 MCP Server para Evolution API

Sistema completo para integração entre Evolution API e Google Analytics 4, composto por:

1. **Servidor MCP Local** (`mcp.js`) - Gera tokens GA4 e fornece dados
2. **Script Evolution** (`from google.js`) - Integra com a plataforma Evolution
3. **Testes Automatizados** (`test-connection.js`) - Valida toda a integração
4. **Integração Python MCP** (`python-mcp/`) - Servidor MCP avançado baseado no repositório harshfolio/mcp-server-ga4

## 🔗 Integração com mcp-server-ga4

Este projeto agora inclui integração com o repositório [harshfolio/mcp-server-ga4](https://github.com/harshfolio/mcp-server-ga4), oferecendo:

- ✅ Servidor MCP completo em Python
- ✅ Ferramentas avançadas para relatórios GA4
- ✅ Suporte a relatórios em tempo real
- ✅ Metadados de métricas e dimensões
- ✅ Compatibilidade com Claude Desktop e outros clientes MCP

## 🚀 Configuração Inicial

### 1. Instalar Dependências

```bash
cd "EVO - MCP"
npm install
```

### 2. Configurar Credenciais GA4

1. Baixe o arquivo JSON da Service Account do Google Cloud Console
2. Renomeie para `credenciais-ga4.json`
3. Coloque na pasta `EVO - MCP`

**Estrutura esperada do arquivo:**
```json
{
  "type": "service_account",
  "project_id": "seu-projeto",
  "private_key_id": "...",
  "private_key": "...",
  "client_email": "...",
  "client_id": "...",
  "auth_uri": "https://accounts.google.com/o/oauth2/auth",
  "token_uri": "https://oauth2.googleapis.com/token"
}
```

### 3. Testar Configuração

```bash
npm test
```

## 🖥️ Uso do Sistema

### Iniciar o Servidor MCP

```bash
npm start
```

O servidor ficará disponível em `http://localhost:3000`

### Endpoints Disponíveis

| Endpoint | Método | Descrição |
|----------|--------|-----------|
| `/health` | GET | Health check do servidor |
| `/token` | POST | Gerar token GA4 |
| `/ga4-data` | POST | Buscar dados GA4 diretamente |
| `/` | POST | Compatibilidade (apenas token) |

## 🤖 Integração com Evolution API

### Configuração no Evolution

1. **Adicione o script** `from google.js` na Evolution API
2. **Configure os secrets** na Evolution:
   - `GA4_CREDENTIALS`: Conteúdo do arquivo JSON da Service Account

### Exemplo de Uso na Evolution

```javascript
// Parâmetros de entrada para o agente
const input = {
  propertyId: "*********",        // ID da propriedade GA4
  method: "mcp",                  // "mcp" ou "direct"
  mcpUrl: "http://localhost:3000", // URL do servidor MCP
  dimensions: [{ name: "date" }],
  metrics: [{ name: "activeUsers" }],
  dateRanges: [{ startDate: "7daysAgo", endDate: "today" }]
};

// O script retornará:
{
  success: true,
  data: { /* dados GA4 */ },
  source: "mcp-direct",
  timestamp: "2024-01-01T12:00:00.000Z"
}
```

## 📊 Métodos de Integração

### 1. Via MCP Server (Recomendado)

- ✅ Cache de tokens (evita regeneração)
- ✅ Múltiplos endpoints
- ✅ Logs detalhados
- ✅ Tratamento de erros robusto

### 2. Geração Direta (Fallback)

- ⚠️ Gera token a cada requisição
- ⚠️ Mais lento
- ✅ Funciona sem servidor local

## 🔧 Configurações Avançadas

### Variáveis de Ambiente

```bash
PORT=3000                    # Porta do servidor MCP
NODE_ENV=production         # Ambiente de execução
```

### Personalização do Script Evolution

```javascript
const input = {
  propertyId: "*********",
  method: "mcp",
  
  // Dimensões personalizadas
  dimensions: [
    { name: "date" },
    { name: "country" },
    { name: "deviceCategory" }
  ],
  
  // Métricas personalizadas
  metrics: [
    { name: "activeUsers" },
    { name: "sessions" },
    { name: "pageviews" }
  ],
  
  // Período personalizado
  dateRanges: [
    { startDate: "30daysAgo", endDate: "today" }
  ]
};
```

## 🧪 Testes e Validação

### Executar Todos os Testes

```bash
npm test
```

### Testes Individuais

```javascript
const tests = require('./test-connection.js');

// Testar apenas credenciais
await tests.testCredentials();

// Testar apenas MCP Server
await tests.testMCPServer();

// Testar integração GA4
await tests.testGA4Integration(token);

// Testar script Evolution
await tests.testEvolutionScript();
```

## 🚨 Solução de Problemas

### Erro: "Arquivo credenciais-ga4.json não encontrado"

1. Verifique se o arquivo está na pasta correta
2. Confirme o nome exato do arquivo
3. Verifique as permissões de leitura

### Erro: "MCP Server não está rodando"

```bash
# Verificar se o servidor está rodando
curl http://localhost:3000/health

# Iniciar o servidor
npm start
```

### Erro: "Property ID inválido"

1. Verifique o Property ID no Google Analytics
2. Use apenas números (ex: *********)
3. Confirme as permissões da Service Account

### Erro: "Token expirado"

- O sistema usa cache automático
- Tokens são renovados automaticamente
- Verifique as credenciais se persistir

## 📝 Logs e Monitoramento

O sistema gera logs detalhados:

```
✅ Credenciais GA4 carregadas com sucesso
🔄 Usando token em cache
🔑 Gerando novo token de acesso...
📊 Buscando dados GA4 para propriedade: *********
✅ Dados obtidos via MCP com sucesso
```

## 🔒 Segurança

- ✅ Tokens são cacheados localmente (não expostos)
- ✅ Credenciais ficam apenas no servidor
- ✅ CORS configurado para segurança
- ✅ Timeouts para evitar travamentos

## 📞 Suporte

Para problemas ou dúvidas:

1. Execute `npm test` para diagnóstico
2. Verifique os logs do servidor
3. Confirme as configurações do GA4
4. Valide as permissões da Service Account
